{"name": "evently", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^5.7.5", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@stripe/stripe-js": "^4.10.0", "@uploadthing/react": "^6.8.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.451.0", "mongodb": "^6.18.0", "mongoose": "^8.17.1", "next": "14.2.4", "query-string": "^9.2.2", "react": "^18.3.1", "react-datepicker": "^7.6.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "stripe": "^15.12.0", "svix": "^1.71.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.13.3", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^20.19.10", "@types/react": "^18.3.23", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.9.2"}}