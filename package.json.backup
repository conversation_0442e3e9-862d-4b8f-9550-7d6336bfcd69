{"name": "evently", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^5.1.5", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.12", "@stripe/stripe-js": "^4.0.0", "@uploadthing/react": "^6.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.395.0", "mongodb": "^6.7.0", "mongoose": "^8.4.3", "next": "14.2.4", "query-string": "^9.0.0", "react": "^18", "react-datepicker": "^7.1.0", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.52.1", "stripe": "^15.12.0", "svix": "^1.24.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.12.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18", "autoprefixer": "^10.4.19", "postcss": "^8", "tailwindcss": "^3.4.4", "typescript": "^5"}}