import type { <PERSON>ada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import "./globals.css";


const poppins = Poppins({
  weight: ["400", "500", "600", "700", "800"],
  subsets: ["latin"],
  variable: "--font-poppins",
});

export const metadata: Metadata = {
  title: "Karaoke Events",
  description: "Register, Promote, and Manage your karaoke events",
  icons: {
    icon: "/images/easy-icon.png",
  },
};

export default function AppRootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={poppins.className}>{children}</body>
      </html>
    </ClerkProvider>
  );
}
