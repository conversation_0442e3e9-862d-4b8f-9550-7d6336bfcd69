@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

* {
  list-style: none;
  padding: 0;
  margin: 0;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-poppins)
}

.filter-grey {
  filter: brightness(0) saturate(100%) invert(47%) sepia(0%) saturate(217%) hue-rotate(32deg) brightness(98%) contrast(92%);
}

/* ========================================== TAILWIND STYLES */
@layer utilities {
  .wrapper {
    @apply max-w-7xl lg:mx-auto p-5 md:px-10 xl:px-0 w-full;
  }

  .flex-center {
    @apply flex justify-center items-center;
  }

  .flex-between {
    @apply flex justify-between items-center;
  }

  /* TYPOGRAPHY */
  /* 64 */
  .h1-bold {
    @apply font-bold text-[40px] leading-[48px] lg:text-[48px] lg:leading-[60px] xl:text-[58px] xl:leading-[74px];
  }

  /* 40 */
  .h2-bold {
    @apply font-bold text-[32px] leading-[40px] lg:text-[36px] lg:leading-[44px] xl:text-[40px] xl:leading-[52px];
  }

  /* 36 */
  .h3-bold {
    @apply font-bold text-[28px] leading-[36px] md:text-[32px] md:leading-[40px];
  }

  /* 32 */
  .h4-medium {
    @apply font-medium text-[24px] leading-[32px] md:text-[28px] md:leading-[36px];
  }

  /* 28 */
  .h5-bold {
    @apply font-bold text-[20px] leading-[28px] md:text-[24px] md:leading-[32px];
  }

  /* 24 */
  .p-bold-24 {
    @apply font-bold text-[24px] leading-[36px];
  }

  /* 20 */
  .p-medium-20 {
    @apply font-medium text-[20px] leading-[30px];
  }

  /* 18 */
  .p-regular-18 {
    @apply font-normal text-[18px] leading-[28px];
  }

  /* 16 */
  .p-medium-16 {
    @apply font-medium text-[16px] leading-[24px];
  }

  /* 14 */
  .p-regular-14 {
    @apply font-normal text-[14px] leading-[20px];
  }

  /* 12 */
  .p-medium-12 {
    @apply font-medium text-[12px] leading-[16px];
  }

  /* SHADCN OVERRIDES */
  .select-field {
    @apply w-full bg-grey-50 h-11 rounded-md px-3 border-none placeholder:text-grey-500;
  }

  .input-field {
    @apply bg-grey-50 h-11 focus-visible:ring-offset-0 focus-visible:ring-transparent !important;
  }

  .textarea {
    @apply bg-grey-50 h-11 focus-visible:ring-offset-0 focus-visible:ring-transparent !important;
  }

  .checkbox {
    @apply border-grey-300 data-[state=checked]:bg-primary-500 data-[state=checked]:text-white;
  }

  .dropdown {
    @apply bg-white w-full rounded-md border-[1.5px] border-stroke p-5 text-dark-500 focus:outline-none bg-transparent;
  }

  .select-item {
    @apply py-3 cursor-pointer focus:bg-primary-50;
  }

  .toggle-switch {
    @apply bg-gray-300 !important;
  }

  /* GLASS MORPHISM EFFECT */
  .glass-morphism {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg;
  }

  /* ANIMATIONS */
  /* Fade in up */
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
  }

  /* Slide in left */
  @keyframes slide-in-left {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  .animate-slide-in-left {
    animation: slide-in-left 0.5s ease-out forwards;
  }

  /* Slide in right */
  @keyframes slide-in-right {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  .animate-slide-in-right {
    animation: slide-in-right 0.5s ease-out forwards;
  }

  /* Pulse slow */
  @keyframes pulse-slow {
    0%, 100% {
      opacity: 0.7;
      transform: scale(1);
    }
    50% {
      opacity: 0.4;
      transform: scale(1.1);
    }
  }
  .animate-pulse-slow {
    animation: pulse-slow 4s ease-in-out infinite;
  }

  /* Shimmer loading */
  .shimmer {
    background: linear-gradient(
      90deg,
      #f0f0f0 25%,
      #e0e0e0 50%,
      #f0f0f0 75%
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Hover scale */
  .hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent;
  }

  /* Bounce animation */
  @keyframes bounce-subtle {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }
  .animate-bounce-subtle {
    animation: bounce-subtle 2s ease-in-out infinite;
  }

  /* Text glow effect */
  .text-glow {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
  }

  /* Text shimmer effect */
  .text-shimmer {
    background: linear-gradient(90deg, #ffffff, #f0f0f0, #ffffff);
    background-size: 200% 100%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: text-shimmer 3s linear infinite;
  }
  @keyframes text-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Responsiveness inside utilities */
  @screen sm {
    .wrapper {
      @apply px-4;
    }
    .h1-bold {
      @apply text-[32px] leading-[40px];
    }
    .h2-bold {
      @apply text-[28px] leading-[36px];
    }
  }
}

/* ========================================== CLERK STYLES */
.cl-logoImage {
  height: 38px;
}

.cl-userButtonBox {
  flex-direction: row-reverse;
}

.cl-userButtonOuterIdentifier {
  font-size: 16px;
}

.cl-userButtonPopoverCard {
  right: 4px !important;
}

.cl-formButtonPrimary:hover,
.cl-formButtonPrimary:focus,
.cl-formButtonPrimary:active {
  background-color: #705CF7
}

/* ========================================== REACT-DATEPICKER STYLES */
.datePicker {
  width: 100%;
}

.react-datepicker__input-container input {
  background-color: transparent;
  width: 100%;
  outline: none;
  margin-left: 16px;
}

.react-datepicker__day--selected {
  background-color: #624cf5 !important;
  color: #ffffff !important;
  border-radius: 4px;
}

.react-datepicker__time-list-item--selected {
  background-color: #624cf5 !important;
}